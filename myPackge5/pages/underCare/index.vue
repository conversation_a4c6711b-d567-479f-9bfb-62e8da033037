<template>
    <view>
        <CustomNavbar :bgColor="'#08BA7E'" :titleColor="'#FFFFFF'" />
        <view class="header">
            <view class="fifter">
                <img src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/xiaoshouhetong/shaixuan.png"
                    alt="" @click="fifterClick" />
            </view>
        </view>

        <!-- Tab切换区域 -->
        <view class="tab-container">
            <scroll-view class="tabs-scroll" scroll-x="true" show-scrollbar="false">
                <view class="tabs">
                    <view
                        class="tab-item"
                        :class="{ 'active': currentTab === 1 }"
                        @click="switchTab(1)"
                    >
                        <!-- <image src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/cow/siyang.png" class="tab-icon" /> -->
                        饲养管理
                    </view>
                    <view
                        class="tab-item"
                        :class="{ 'active': currentTab === 2 }"
                        @click="switchTab(2)"
                    >
                        <!-- <image src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/cow/jiankong.png" class="tab-icon" /> -->
                        生长监测
                    </view>
                    <view
                        class="tab-item"
                        :class="{ 'active': currentTab === 3 }"
                        @click="switchTab(3)"
                    >
                        <!-- <image src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/cow/fangkong.png" class="tab-icon" /> -->
                        疾病防控
                    </view>
                    <view
                        class="tab-item"
                        :class="{ 'active': currentTab === 4 }"
                        @click="switchTab(4)"
                    >
                        <!-- <image src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/cow/jilu.png" class="tab-icon" /> -->
                        日常记录
                    </view>
                </view>
            </scroll-view>
        </view>

        <!-- Tab内容区域 -->
        <view class="tab-content">
            <FeedingManagement v-if="currentTab === 1" />
            <GrowthMonitoring v-if="currentTab === 2" />
            <DiseaseControl v-if="currentTab === 3" />
            <DailyRecord v-if="currentTab === 4" />
        </view>

        <view class="Add" @click="addIntent" v-if="$hasPermi('nmb:saleContract:add')">
            <img src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/xiaoshouhetong/add.png" alt="" />
        </view>
        <filterPopup @resetSearch="resetSearch" :pickerFilterShow="pickerFilterShow" @canel="pickerFilterShow = false"
            @submitForm="submitForm" />
    </view>
</template>

<script>
import filterPopup from '@/components/filterPopup/index.vue'
// import contractList from '../../../myPackge1/pages/salesContract/component/contractList.vue'
import nullList from '@/components/null-list/index.vue'
import { mapState } from 'vuex'
import { saleContractPage } from '@/api/pages/salesContract'
import CustomNavbar from '@/components/uni-custom-navbar/CustomNavbar.vue'
import FeedingManagement from './components/FeedingManagement.vue'
import GrowthMonitoring from './components/GrowthMonitoring.vue'
import DiseaseControl from './components/DiseaseControl.vue'
import DailyRecord from './components/DailyRecord.vue'

export default {
    components: {
        CustomNavbar,
        filterPopup,
        nullList,
        FeedingManagement,
        GrowthMonitoring,
        DiseaseControl,
        DailyRecord
        // contractList
    },
    data() {
        return {
            isIphonex: getApp().globalData.systemInfo.isIphonex,
            isEmpty: false,
            pickerFilterShow: false,
            filters: {},
            list: [],
            noMore: false,
            pageSize: 10,
            pageNum: 1,
            refresherState: false,
            scrollTop: 0,
            currentTab: 1, // 当前选中的tab
        }
    },
    onLoad() {
        this.getList()
        uni.$on('updateSalesContractList', () => {
            this.getList();
            console.log('updateSalesContractList')
        })
        console.log('getList')
    },
    onUnload() {
        uni.$off('updateSalesContractList');
    },
    onShow() { },
    computed: {
        ...mapState({
            userInfo: (state) => state.userDetail.user,
        }),
    },
    methods: {
        // Tab切换方法
        switchTab(tabIndex) {
            this.currentTab = tabIndex;
        },

        getList(val) {
            uni.showLoading({
                title: '加载中',
                icon: 'none',
            })
            const params = {
                pageNum: this.pageNum,
                pageSize: this.pageSize,
                ...val
            }
            saleContractPage(params).then(res => {
                let pendingApproval = res.result?.list || [];
                let total = res.result.total || 0
                if (this.pageNum >= 2) {
                    this.list = this.list.concat(pendingApproval)
                    this.list.length >= total ? this.noMore = true : this.noMore = false;
                } else {
                    if (total >= 1) {
                        this.isEmpty = false;
                        this.list = pendingApproval;
                        this.list.length >= total ? this.noMore = true : this.noMore = false;
                    } else {
                        this.isEmpty = true;
                    }
                }
            })
            uni.hideLoading()
        },
        scrollToLower() {
            if (this.noMore) return
            this.pageNum++
            this.getList();
        },
        bindrefresherrefresh() {
            this.refresherState = true
            this.pageNum = 1
            this.noMore = false
            setTimeout(() => {
                this.refresherState = false
                this.getList()
                this.$toast('刷新成功')
            }, 1000)
        },

        // 搜索
        fifterClick() {
            this.pickerFilterShow = true
        },

        addIntent() {
            uni.navigateTo({
                url: `/myPackge1/pages/salesContract/createContract`,
            })
        },
        resetSearch() {
            this.getList()
        },
        submitForm(val) {
            console.log(val)
            this.pickerFilterShow = false
            this.getList(val)
        },
    },
}
</script>

<style lang="scss" scoped>
.header {
    width: 750rpx;
    height: 727rpx;
    display: flex;
    padding-top: 120rpx;
    /* 导航栏空间 */
    box-sizing: border-box;
    position: relative;
    background: url(https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/cow/bg.png) no-repeat 100% 100%;
    background-size: 100% 100%;
    position: relative;
}

.fifter {
    position: absolute;
    top: 195rpx;
    right: 30rpx;

    img {
        width: 34rpx;
        height: 32.5rpx;
    }
}

.tab-container {
    margin-top: -372rpx;
    padding: 0 30rpx;
    margin-bottom: 30rpx;
}

.tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tab-item {
    width: 188rpx;
    height: 65rpx;
    background: #FFFFFF;
    border-radius: 32.5rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #1DB17A;
    font-weight: 500;
    font-size: 26rpx;
    position: relative;
    transition: all 0.3s ease;

    .tab-icon {
        width: 24rpx;
        height: 24rpx;
        margin-bottom: 4rpx;
    }

    &.active {
        color: #FFFFFF;
        background: linear-gradient(140deg, #1CC271 0%, #5CD26F 100%);

        // 气泡效果
        &::before {
            content: '';
            position: absolute;
            bottom: -8rpx;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 8rpx solid transparent;
            border-right: 8rpx solid transparent;
            border-top: 8rpx solid #5CD26F;
        }
    }
}

.tab-content {
    padding: 0 30rpx;
    min-height: 400rpx;
}

.main {
    margin-top: -372rpx;
}

.Add {
    width: 152rpx;
    height: 145rpx;
    position: absolute;
    bottom: 290rpx;
    right: 10rpx;

    img {
        width: 152rpx;
        height: 145rpx;
    }
}
</style>
